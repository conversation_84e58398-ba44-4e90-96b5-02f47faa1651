import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_colors.dart';
import '../../models/document_model.dart';
import 'empty_state_widget.dart';

enum FileTableMode {
  view, // View only (like home screen)
  select, // Selection mode (like add files to category)
  manage, // Management mode (like category files)
}

enum TableColumnType {
  checkbox,
  fileName,
  fileType,
  fileSize,
  uploadDate,
  owner,
  status,
  actions,
  custom,
}

class TableColumn {
  final TableColumnType type;
  final String title;
  final TableColumnWidth width;
  final TextAlign alignment;
  final bool isCheckbox;
  final String Function(DocumentModel)? customValue;

  const TableColumn({
    required this.type,
    required this.title,
    required this.width,
    this.alignment = TextAlign.left,
    this.isCheckbox = false,
    this.customValue,
  });
}

class FileTableWidget extends StatefulWidget {
  final List<DocumentModel> documents;
  final FileTableMode mode;
  final String title;
  final bool isLoading;
  final bool showFilter;
  final bool showRefresh;
  final bool showSelectAll;
  final Set<String>? selectedFiles;
  final VoidCallback? onRefresh;
  final VoidCallback? onFilter;
  final Function(DocumentModel)? onDocumentTap;
  final Function(DocumentModel)? onDocumentMenu;
  final Function(String, bool)? onFileSelect;
  final Function(bool?)? onSelectAll;
  final Widget? emptyStateWidget;
  final List<TableColumn>? customColumns;
  final double? maxHeight;
  final bool enableScroll;
  final double? bottomSpacing;
  final BorderRadius? borderRadius;

  const FileTableWidget({
    super.key,
    required this.documents,
    this.mode = FileTableMode.view,
    this.title = 'Files',
    this.isLoading = false,
    this.showFilter = true,
    this.showRefresh = false,
    this.showSelectAll = false,
    this.selectedFiles,
    this.onRefresh,
    this.onFilter,
    this.onDocumentTap,
    this.onDocumentMenu,
    this.onFileSelect,
    this.onSelectAll,
    this.emptyStateWidget,
    this.customColumns,
    this.maxHeight,
    this.enableScroll = true,
    this.bottomSpacing,
    this.borderRadius,
  });

  @override
  State<FileTableWidget> createState() => _FileTableWidgetState();
}

class _FileTableWidgetState extends State<FileTableWidget> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and actions
          if (widget.title.isNotEmpty) ...[
            _buildHeader(),
            const SizedBox(height: 16),
          ],
          // Modern file list container
          Container(
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Table Header
                _buildTableHeader(),
                // Table Content
                widget.isLoading
                    ? Container(
                        height: 200,
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: AppColors.primary,
                          ),
                        ),
                      )
                    : widget.documents.isEmpty
                    ? _buildEmptyState()
                    : _buildTableContent(),
              ],
            ),
          ),
          SizedBox(
            height: widget.bottomSpacing ?? 100,
            child: const SizedBox.shrink(), // Configurable bottom spacing
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.title,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        Row(
          children: [
            if (widget.showRefresh && widget.onRefresh != null)
              IconButton(
                onPressed: widget.onRefresh,
                icon: const Icon(
                  Icons.refresh,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                tooltip: 'Refresh',
              ),
            if (widget.showFilter && widget.onFilter != null)
              IconButton(
                onPressed: widget.onFilter,
                icon: const Icon(
                  Icons.filter_list,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                tooltip: 'Filter Files',
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildTableHeader() {
    final columns = widget.customColumns ?? _getDefaultColumns();
    final borderRadius = widget.borderRadius ?? BorderRadius.circular(16);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.only(
          topLeft: borderRadius.topLeft,
          topRight: borderRadius.topRight,
        ),
        border: Border(
          bottom: BorderSide(
            color: AppColors.border.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Table(
        columnWidths: _getColumnWidths(columns),
        children: [
          TableRow(
            children: columns.map((column) {
              if (column.isCheckbox && widget.mode == FileTableMode.select) {
                return TableCell(
                  child: widget.showSelectAll
                      ? Checkbox(
                          value:
                              widget.selectedFiles?.length ==
                                  widget.documents.length &&
                              widget.documents.isNotEmpty,
                          onChanged: widget.onSelectAll,
                          activeColor: AppColors.primary,
                        )
                      : const SizedBox.shrink(),
                );
              }
              return TableCell(
                child: Text(
                  column.title,
                  style: _getTableHeaderStyle(),
                  textAlign: column.alignment,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTableContent() {
    final borderRadius = widget.borderRadius ?? BorderRadius.circular(16);
    final content = Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.only(
          bottomLeft: borderRadius.bottomLeft,
          bottomRight: borderRadius.bottomRight,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ...widget.documents.map((document) => _buildDocumentRow(document)),
          const SizedBox(height: 8), // Bottom padding
        ],
      ),
    );

    // If scroll is enabled and maxHeight is specified, wrap in scrollable container
    if (widget.enableScroll && widget.maxHeight != null) {
      return ConstrainedBox(
        constraints: BoxConstraints(maxHeight: widget.maxHeight!),
        child: Scrollbar(
          controller: _scrollController,
          thumbVisibility: true,
          trackVisibility: true,
          thickness: 6.0,
          radius: const Radius.circular(3.0),
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const ClampingScrollPhysics(),
            child: content,
          ),
        ),
      );
    }

    // If scroll is enabled but no maxHeight specified, calculate dynamic height
    if (widget.enableScroll) {
      return LayoutBuilder(
        builder: (context, constraints) {
          // Calculate available height dynamically
          final screenHeight = MediaQuery.of(context).size.height;
          final appBarHeight = AppBar().preferredSize.height;
          final statusBarHeight = MediaQuery.of(context).padding.top;
          final bottomNavHeight = 80.0; // Approximate bottom navigation height
          final bottomSpacing = widget.bottomSpacing ?? 100;
          final headerHeight = widget.title.isNotEmpty
              ? 60.0
              : 0.0; // Approximate header height
          final tableHeaderHeight = 50.0; // Approximate table header height

          final availableHeight =
              screenHeight -
              statusBarHeight -
              appBarHeight -
              bottomNavHeight -
              bottomSpacing -
              headerHeight -
              tableHeaderHeight -
              32; // Additional padding

          final maxHeight = availableHeight > 200 ? availableHeight : 400.0;

          return ConstrainedBox(
            constraints: BoxConstraints(maxHeight: maxHeight),
            child: Scrollbar(
              controller: _scrollController,
              thumbVisibility: true,
              trackVisibility: true,
              thickness: 6.0,
              radius: const Radius.circular(3.0),
              child: SingleChildScrollView(
                controller: _scrollController,
                physics: const ClampingScrollPhysics(),
                child: content,
              ),
            ),
          );
        },
      );
    }

    // Return original content without scroll
    return content;
  }

  Widget _buildDocumentRow(DocumentModel document) {
    final columns = widget.customColumns ?? _getDefaultColumns();
    final isSelected = widget.selectedFiles?.contains(document.id) ?? false;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.primary.withValues(alpha: 0.08)
            : AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.3)
              : AppColors.border.withValues(alpha: 0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: widget.onDocumentTap != null
              ? () => widget.onDocumentTap!(document)
              : null,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Table(
              columnWidths: _getColumnWidths(columns),
              children: [
                TableRow(
                  children: columns.map((column) {
                    return TableCell(
                      verticalAlignment: TableCellVerticalAlignment.middle,
                      child: _buildCellContent(column, document),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCellContent(TableColumn column, DocumentModel document) {
    switch (column.type) {
      case TableColumnType.checkbox:
        if (widget.mode == FileTableMode.select) {
          final isSelected =
              widget.selectedFiles?.contains(document.id) ?? false;
          return Checkbox(
            value: isSelected,
            onChanged: widget.onFileSelect != null
                ? (value) => widget.onFileSelect!(document.id, value ?? false)
                : null,
            activeColor: AppColors.primary,
          );
        }
        return const SizedBox.shrink();

      case TableColumnType.fileName:
        return Row(
          children: [
            // Enhanced file type indicator
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    _getFileTypeColor(document.fileType).withValues(alpha: 0.8),
                    _getFileTypeColor(document.fileType).withValues(alpha: 0.6),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: _getFileTypeColor(
                      document.fileType,
                    ).withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Icon(
                  _getFileTypeIcon(document.fileType),
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    document.fileName,
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _formatFileSize(document.fileSize),
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );

      case TableColumnType.fileType:
        return Text(
          _getFileTypeLabel(document.fileType),
          style: _getTableTextStyle(),
          textAlign: column.alignment,
        );

      case TableColumnType.fileSize:
        return Text(
          _formatFileSize(document.fileSize),
          style: _getTableTextStyle(),
          textAlign: column.alignment,
        );

      case TableColumnType.uploadDate:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _formatDate(document.uploadedAt),
              style: GoogleFonts.poppins(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 2),
            Text(
              _formatTime(document.uploadedAt),
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w400,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.right,
            ),
          ],
        );

      case TableColumnType.owner:
        return Text(
          document.uploadedBy,
          style: _getTableTextStyle(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: column.alignment,
        );

      case TableColumnType.status:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(document.status).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            document.status.toUpperCase(),
            style: GoogleFonts.poppins(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(document.status),
            ),
          ),
        );

      case TableColumnType.actions:
        return Center(
          child: Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.border.withValues(alpha: 0.5),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: widget.onDocumentMenu != null
                  ? () => widget.onDocumentMenu!(document)
                  : null,
              icon: const Icon(
                Icons.more_vert,
                color: AppColors.textSecondary,
                size: 18,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
            ),
          ),
        );

      case TableColumnType.custom:
        return Text(
          column.customValue?.call(document) ?? '',
          style: _getTableTextStyle(),
          textAlign: column.alignment,
        );
    }
  }

  Widget _buildEmptyState() {
    if (widget.emptyStateWidget != null) {
      return widget.emptyStateWidget!;
    }

    return const FileTableEmptyState();
  }

  // Helper methods
  List<TableColumn> _getDefaultColumns() {
    switch (widget.mode) {
      case FileTableMode.select:
        return [
          TableColumn(
            type: TableColumnType.checkbox,
            title: '',
            width: const FixedColumnWidth(1000),
            isCheckbox: true,
          ),
          TableColumn(
            type: TableColumnType.fileName,
            title: 'File Name',
            width: const FlexColumnWidth(10),
          ),
          TableColumn(
            type: TableColumnType.fileType,
            title: 'Type',
            width: const FlexColumnWidth(10),
          ),
          TableColumn(
            type: TableColumnType.uploadDate,
            title: 'Date',
            width: const FlexColumnWidth(10),
          ),
        ];

      case FileTableMode.manage:
        return [
          TableColumn(
            type: TableColumnType.fileName,
            title: 'Name',
            width: const FlexColumnWidth(4),
          ),
          TableColumn(
            type: TableColumnType.fileType,
            title: 'Type',
            width: const FlexColumnWidth(2),
            alignment: TextAlign.center,
          ),
          TableColumn(
            type: TableColumnType.uploadDate,
            title: 'Date',
            width: const FlexColumnWidth(2),
            alignment: TextAlign.center,
          ),
          TableColumn(
            type: TableColumnType.actions,
            title: 'Action',
            width: const FixedColumnWidth(50),
            alignment: TextAlign.center,
          ),
        ];

      case FileTableMode.view:
        return [
          TableColumn(
            type: TableColumnType.fileName,
            title: 'Name',
            width: const FlexColumnWidth(5),
          ),
          TableColumn(
            type: TableColumnType.uploadDate,
            title: 'Date',
            width: const FlexColumnWidth(1),
            alignment: TextAlign.center,
          ),
          TableColumn(
            type: TableColumnType.actions,
            title: 'Action',
            width: const FixedColumnWidth(50),
            alignment: TextAlign.center,
          ),
        ];
    }
  }

  Map<int, TableColumnWidth> _getColumnWidths(List<TableColumn> columns) {
    final Map<int, TableColumnWidth> widths = {};
    for (int i = 0; i < columns.length; i++) {
      widths[i] = columns[i].width;
    }
    return widths;
  }

  TextStyle _getTableHeaderStyle() {
    return GoogleFonts.poppins(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: AppColors.textPrimary,
    );
  }

  TextStyle _getTableTextStyle() {
    return GoogleFonts.poppins(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: AppColors.textPrimary,
    );
  }

  IconData _getFileTypeIcon(String fileType) {
    final lowerFileType = fileType.toLowerCase();

    // PDF files
    if (lowerFileType.contains('pdf')) {
      return Icons.picture_as_pdf;
    }

    // Word documents (doc, docx)
    if (lowerFileType.contains('doc') ||
        lowerFileType.contains('word') ||
        lowerFileType.contains('msword')) {
      return Icons.description;
    }

    // Excel files (xlsx, xls, spreadsheet)
    if (lowerFileType.contains('xlsx') ||
        lowerFileType.contains('xls') ||
        lowerFileType.contains('excel') ||
        lowerFileType.contains('sheet') ||
        lowerFileType.contains('spreadsheet')) {
      return Icons.table_chart;
    }

    // PowerPoint files (ppt, pptx)
    if (lowerFileType.contains('ppt') ||
        lowerFileType.contains('powerpoint') ||
        lowerFileType.contains('presentation')) {
      return Icons.slideshow;
    }

    // Image files (jpg, png, jpeg, gif)
    if (lowerFileType.contains('image') ||
        lowerFileType.contains('jpg') ||
        lowerFileType.contains('jpeg') ||
        lowerFileType.contains('png') ||
        lowerFileType.contains('gif')) {
      return Icons.image;
    }

    // Video files
    if (lowerFileType.contains('video')) {
      return Icons.video_file;
    }

    // Audio files
    if (lowerFileType.contains('audio')) {
      return Icons.audio_file;
    }

    // Default file icon
    return Icons.insert_drive_file;
  }

  Color _getFileTypeColor(String fileType) {
    final lowerFileType = fileType.toLowerCase();

    // PDF files
    if (lowerFileType.contains('pdf')) {
      return Colors.red;
    }

    // Word documents (doc, docx)
    if (lowerFileType.contains('doc') ||
        lowerFileType.contains('word') ||
        lowerFileType.contains('msword')) {
      return Colors.blue;
    }

    // Excel files (xlsx, xls, spreadsheet)
    if (lowerFileType.contains('xlsx') ||
        lowerFileType.contains('xls') ||
        lowerFileType.contains('excel') ||
        lowerFileType.contains('sheet') ||
        lowerFileType.contains('spreadsheet')) {
      return Colors.green;
    }

    // PowerPoint files (ppt, pptx)
    if (lowerFileType.contains('ppt') ||
        lowerFileType.contains('powerpoint') ||
        lowerFileType.contains('presentation')) {
      return Colors.orange;
    }

    // Image files (jpg, png, jpeg, gif)
    if (lowerFileType.contains('image') ||
        lowerFileType.contains('jpg') ||
        lowerFileType.contains('jpeg') ||
        lowerFileType.contains('png') ||
        lowerFileType.contains('gif')) {
      return Colors.purple;
    }

    // Video files
    if (lowerFileType.contains('video')) {
      return Colors.pink;
    }

    // Audio files
    if (lowerFileType.contains('audio')) {
      return Colors.teal;
    }

    // Default color
    return AppColors.textSecondary;
  }

  String _getFileTypeLabel(String fileType) {
    final lowerFileType = fileType.toLowerCase();

    // PDF files
    if (lowerFileType.contains('pdf')) {
      return 'PDF';
    }

    // Word documents (doc, docx)
    if (lowerFileType.contains('doc') ||
        lowerFileType.contains('word') ||
        lowerFileType.contains('msword')) {
      return 'DOC';
    }

    // Excel files (xlsx, xls, spreadsheet)
    if (lowerFileType.contains('xlsx') ||
        lowerFileType.contains('xls') ||
        lowerFileType.contains('excel') ||
        lowerFileType.contains('sheet') ||
        lowerFileType.contains('spreadsheet')) {
      return 'XLS';
    }

    // PowerPoint files (ppt, pptx)
    if (lowerFileType.contains('ppt') ||
        lowerFileType.contains('powerpoint') ||
        lowerFileType.contains('presentation')) {
      return 'PPT';
    }

    // Image files (jpg, png, jpeg, gif)
    if (lowerFileType.contains('image') ||
        lowerFileType.contains('jpg') ||
        lowerFileType.contains('jpeg') ||
        lowerFileType.contains('png') ||
        lowerFileType.contains('gif')) {
      return 'IMG';
    }

    // Video files
    if (lowerFileType.contains('video')) {
      return 'VID';
    }

    // Audio files
    if (lowerFileType.contains('audio')) {
      return 'AUD';
    }

    // Default label
    return 'FILE';
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'published':
        return AppColors.success;
      case 'pending':
      case 'processing':
        return AppColors.warning;
      case 'inactive':
      case 'draft':
        return AppColors.textSecondary;
      case 'error':
      case 'failed':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    }
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    }
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('MMM dd, yyyy').format(date);
    }
  }

  String _formatTime(DateTime date) {
    return DateFormat('HH:mm').format(date);
  }
}
